{"name": "electron-screenshots", "version": "1.1.5", "description": "electron 截图插件", "types": "lib/index.d.ts", "main": "lib/index.cjs.js", "module": "lib/index.js", "files": ["lib/**"], "scripts": {"prepublishOnly": "npm run build", "start": "cross-env DEBUG=electron-screenshots electron lib/demo.js", "dev": "tsc --sourceMap --watch", "build": "npm run lint && npm run clean && tsc", "lint": "eslint . --ext .js,.ts --fix", "clean": "<PERSON><PERSON><PERSON> lib"}, "repository": {"type": "git", "url": "git+https://github.com/nashaofu/screenshots.git"}, "keywords": ["electron", "shortcut", "screenshot", "cropper"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nashaofu/screenshots/issues"}, "homepage": "https://github.com/nashaofu/screenshots/tree/master/packages/electron-screenshots#readme", "private": false, "publishConfig": {"registry": "http://localhost:4873/"}, "dependencies": {"debug": "^4.3.4", "fs-extra": "^11.1.1", "node-screenshots": "^0.2.1", "react-screenshots": "^1.1.5"}, "peerDependencies": {"electron": ">=14"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^11.0.1", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "cross-env": "^7.0.3", "electron": "^23.2.0", "eslint": "^8.37.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-plugin-import": "^2.27.5", "rimraf": "^4.4.1", "typescript": "^5.0.2"}}