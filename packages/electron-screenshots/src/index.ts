import debug, { Debugger } from 'debug';
import {
  BrowserView,
  BrowserWindow,
  clipboard,
  desktopCapturer,
  dialog,
  ipcMain,
  nativeImage,
} from 'electron';
import Events from 'events';
import fs from 'fs-extra';
import Event from './event';
import getDisplay, { Display } from './getDisplay';
import padStart from './padStart';
import { Bounds, ScreenshotsData } from './preload';

export type LoggerFn = (...args: unknown[]) => void;
export type Logger = Debugger | LoggerFn;

export interface Lang {
  magnifier_position_label?: string;
  operation_ok_title?: string;
  operation_cancel_title?: string;
  operation_save_title?: string;
  operation_redo_title?: string;
  operation_undo_title?: string;
  operation_mosaic_title?: string;
  operation_text_title?: string;
  operation_brush_title?: string;
  operation_arrow_title?: string;
  operation_ellipse_title?: string;
  operation_rectangle_title?: string;
}

export interface ScreenshotsOpts {
  lang?: Lang;
  logger?: Logger;
  singleWindow?: boolean;
}

export { Bounds };

export default class Screenshots extends Events {
  // 截图窗口对象
  public $win: BrowserWindow | null = null;

  public $view: BrowserView;

  private logger: Logger;

  private singleWindow: boolean;

  private isReady: Promise<void>;

  // 静态变量用于管理全局IPC监听器
  private static ipcListenersRegistered = false;

  constructor(opts?: ScreenshotsOpts) {
    super();
    this.logger = opts?.logger || debug('electron-screenshots');
    this.singleWindow = opts?.singleWindow || false;

    // 创建新的BrowserView实例
    this.$view = new BrowserView({
      webPreferences: {
        preload: require.resolve('./preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
      },
    });

    // 初始化isReady Promise
    this.isReady = new Promise<void>((resolve) => {
      const readyHandler = () => {
        this.logger('SCREENSHOTS:ready');
        resolve();
      };
      ipcMain.once('SCREENSHOTS:ready', readyHandler);
    });

    this.listenIpc();
    this.$view.webContents.loadURL(
      `file://${require.resolve('react-screenshots/electron/electron.html')}`,
    );

    if (opts?.lang) {
      this.setLang(opts.lang);
    }
  }

  /**
   * 开始截图
   */
  public async startCapture(): Promise<void> {
    this.logger('startCapture');

    // 设置当前实例为活跃实例
    this.setAsActiveInstance();

    const display = getDisplay();

    const [imageUrl] = await Promise.all([this.capture(display), this.isReady]);

    await this.createWindow(display);

    this.$view.webContents.send('SCREENSHOTS:capture', display, imageUrl);
  }

  /**
   * 结束截图
   */
  public async endCapture(): Promise<void> {
    this.logger('endCapture');
    await this.reset();

    // 清理活跃实例
    if (Screenshots.activeInstance === this) {
      Screenshots.activeInstance = null;
    }

    if (!this.$win) {
      return;
    }

    // 先清除 Kiosk 模式，然后取消全屏才有效
    this.$win.setKiosk(false);
    this.$win.blur();
    this.$win.blurWebView();
    this.$win.unmaximize();
    this.$win.removeBrowserView(this.$view);

    if (this.singleWindow) {
      this.$win.hide();
    } else {
      this.$win.destroy();
    }
  }

  /**
   * 销毁Screenshots实例，清理资源
   */
  public destroy(): void {
    this.logger('destroy');

    // 销毁BrowserView
    if (this.$view && !this.$view.webContents.isDestroyed()) {
      this.$view.webContents.destroy();
    }

    // 销毁窗口
    if (this.$win && !this.$win.isDestroyed()) {
      this.$win.destroy();
      this.$win = null;
    }
  }

  /**
   * 设置语言
   */
  public async setLang(lang: Partial<Lang>): Promise<void> {
    this.logger('setLang', lang);

    await this.isReady;

    this.$view.webContents.send('SCREENSHOTS:setLang', lang);
  }

  private async reset() {
    // 重置截图区域
    this.$view.webContents.send('SCREENSHOTS:reset');

    // 保证 UI 有足够的时间渲染
    await Promise.race([
      new Promise<void>((resolve) => {
        setTimeout(() => resolve(), 500);
      }),
      new Promise<void>((resolve) => {
        ipcMain.once('SCREENSHOTS:reset', () => resolve());
      }),
    ]);
  }

  /**
   * 初始化窗口
   */
  private async createWindow(display: Display): Promise<void> {
    // 重置截图区域
    await this.reset();

    // 复用未销毁的窗口
    if (!this.$win || this.$win?.isDestroyed?.()) {
      const windowTypes: Record<string, string | undefined> = {
        darwin: 'panel',
        // linux 必须设置为 undefined，否则会在部分系统上不能触发focus 事件
        // https://github.com/nashaofu/screenshots/issues/203#issuecomment-1518923486
        linux: undefined,
        win32: 'toolbar',
      };

      this.$win = new BrowserWindow({
        title: 'screenshots',
        x: display.x,
        y: display.y,
        width: display.width,
        height: display.height,
        useContentSize: true,
        type: windowTypes[process.platform],
        frame: false,
        show: false,
        autoHideMenuBar: true,
        transparent: true,
        resizable: false,
        movable: false,
        minimizable: false,
        maximizable: false,
        // focusable 必须设置为 true, 否则窗口不能及时响应esc按键，输入框也不能输入
        focusable: true,
        skipTaskbar: true,
        alwaysOnTop: true,
        /**
         * linux 下必须设置为false，否则不能全屏显示在最上层
         * mac 下设置为false，否则可能会导致程序坞不恢复问题，且与 kiosk 模式冲突
         */
        fullscreen: false,
        // mac fullscreenable 设置为 true 会导致应用崩溃
        fullscreenable: false,
        kiosk: true,
        backgroundColor: '#00000000',
        titleBarStyle: 'hidden',
        hasShadow: false,
        paintWhenInitiallyHidden: false,
        // mac 特有的属性
        roundedCorners: false,
        enableLargerThanScreen: false,
        acceptFirstMouse: true,
      });

      this.emit('windowCreated', this.$win);
      this.$win.on('show', () => {
        this.$win?.focus();
        this.$win?.setKiosk(true);
      });

      this.$win.on('closed', () => {
        this.emit('windowClosed', this.$win);
        this.$win = null;
      });
    }

    this.$win.setBrowserView(this.$view);

    // 适定平台
    if (process.platform === 'darwin') {
      this.$win.setWindowButtonVisibility(false);
    }

    if (process.platform !== 'win32') {
      this.$win.setVisibleOnAllWorkspaces(true, {
        visibleOnFullScreen: true,
        skipTransformProcessType: true,
      });
    }

    this.$win.blur();
    this.$win.setBounds(display);
    this.$view.setBounds({
      x: 0,
      y: 0,
      width: display.width,
      height: display.height,
    });
    this.$win.setAlwaysOnTop(true);
    this.$win.show();
  }

  private async capture(display: Display): Promise<string> {
    this.logger('SCREENSHOTS:capture');

    try {
      const { Monitor } = await import('node-screenshots');
      const monitor = Monitor.fromPoint(
        display.x + display.width / 2,
        display.y + display.height / 2,
      );
      this.logger(
        'SCREENSHOTS:capture Monitor.fromPoint arguments %o',
        display,
      );
      this.logger('SCREENSHOTS:capture Monitor.fromPoint return %o', {
        id: monitor?.id,
        name: monitor?.name,
        x: monitor?.x,
        y: monitor?.y,
        width: monitor?.width,
        height: monitor?.height,
        rotation: monitor?.rotation,
        scaleFactor: monitor?.scaleFactor,
        frequency: monitor?.frequency,
        isPrimary: monitor?.isPrimary,
      });

      if (!monitor) {
        throw new Error(`Monitor.fromDisplay(${display.id}) get null`);
      }

      const image = await monitor.captureImage();
      const buffer = await image.toPng(true);
      return `data:image/png;base64,${buffer.toString('base64')}`;
    } catch (err) {
      this.logger('SCREENSHOTS:capture Monitor capture() error %o', err);

      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: {
          width: display.width * display.scaleFactor,
          height: display.height * display.scaleFactor,
        },
      });

      let source;
      // Linux系统上，screen.getDisplayNearestPoint 返回的 Display 对象的 id
      // 和这里 source 对象上的 display_id(Linux上，这个值是空字符串) 或 id 的中间部分，都不一致
      // 但是，如果只有一个显示器的话，其实不用判断，直接返回就行
      if (sources.length === 1) {
        [source] = sources;
      } else {
        source = sources.find(
          (item) => item.display_id === display.id.toString()
            || item.id.startsWith(`screen:${display.id}:`),
        );
      }

      if (!source) {
        this.logger(
          "SCREENSHOTS:capture Can't find screen source. sources: %o, display: %o",
          sources,
          display,
        );
        throw new Error("Can't find screen source");
      }

      return source.thumbnail.toDataURL();
    }
  }

  /**
   * 绑定ipc时间处理
   */
  private listenIpc(): void {
    // 使用静态变量确保全局只注册一次IPC监听器
    if (Screenshots.ipcListenersRegistered) {
      return;
    }

    Screenshots.ipcListenersRegistered = true;

    // 先移除已存在的监听器，避免重复注册
    ipcMain.removeAllListeners('SCREENSHOTS:ok');
    ipcMain.removeAllListeners('SCREENSHOTS:cancel');
    ipcMain.removeAllListeners('SCREENSHOTS:save');

    /**
     * OK事件 - 使用静态方法处理，避免this绑定问题
     */
    ipcMain.on('SCREENSHOTS:ok', Screenshots.handleOkEvent);

    /**
     * CANCEL事件 - 使用静态方法处理，避免this绑定问题
     */
    ipcMain.on('SCREENSHOTS:cancel', Screenshots.handleCancelEvent);

    /**
     * SAVE事件 - 使用静态方法处理，避免this绑定问题
     */
    ipcMain.on('SCREENSHOTS:save', Screenshots.handleSaveEvent);
  }

  // 静态变量用于存储当前活跃的Screenshots实例
  private static activeInstance: Screenshots | null = null;

  // 设置当前活跃的实例
  private setAsActiveInstance(): void {
    Screenshots.activeInstance = this;
  }

  // 静态事件处理方法
  private static handleOkEvent = (e: any, buffer: Buffer, data: ScreenshotsData) => {
    const instance = Screenshots.activeInstance;
    if (!instance) return;

    instance.logger(
      'SCREENSHOTS:ok buffer.length %d, data: %o',
      buffer.length,
      data,
    );

    const event = new Event();
    instance.emit('ok', event, buffer, data);
    if (event.defaultPrevented) {
      return;
    }
    clipboard.writeImage(nativeImage.createFromBuffer(buffer));
    instance.endCapture();
  };

  private static handleCancelEvent = () => {
    const instance = Screenshots.activeInstance;
    if (!instance) return;

    instance.logger('SCREENSHOTS:cancel');

    const event = new Event();
    instance.emit('cancel', event);
    if (event.defaultPrevented) {
      return;
    }
    instance.endCapture();
  };

  private static handleSaveEvent = async (e: any, buffer: Buffer, data: ScreenshotsData) => {
    const instance = Screenshots.activeInstance;
    if (!instance) return;

    instance.logger(
      'SCREENSHOTS:save buffer.length %d, data: %o',
      buffer.length,
      data,
    );

    const event = new Event();
    instance.emit('save', event, buffer, data);
    if (event.defaultPrevented || !instance.$win) {
      return;
    }

    const time = new Date();
    const year = time.getFullYear();
    const month = padStart(time.getMonth() + 1, 2, '0');
    const date = padStart(time.getDate(), 2, '0');
    const hours = padStart(time.getHours(), 2, '0');
    const minutes = padStart(time.getMinutes(), 2, '0');
    const seconds = padStart(time.getSeconds(), 2, '0');
    const milliseconds = padStart(time.getMilliseconds(), 3, '0');

    instance.$win.setAlwaysOnTop(false);

    const { canceled, filePath } = await dialog.showSaveDialog(instance.$win, {
      defaultPath: `${year}${month}${date}${hours}${minutes}${seconds}${milliseconds}.png`,
      filters: [
        { name: 'Image (png)', extensions: ['png'] },
        { name: 'All Files', extensions: ['*'] },
      ],
    });

    if (!instance.$win) {
      instance.emit('afterSave', new Event(), buffer, data, false); // isSaved = false
      return;
    }

    instance.$win.setAlwaysOnTop(true);
    if (canceled || !filePath) {
      instance.emit('afterSave', new Event(), buffer, data, false); // isSaved = false
      return;
    }

    await fs.writeFile(filePath, buffer);
    instance.emit('afterSave', new Event(), buffer, data, true); // isSaved = true
    instance.endCapture();
  };
}
