{"name": "react-screenshots", "version": "0.5.22", "description": "a screenshot cropper tool by react", "main": "./lib/react-screenshots.cjs.js", "module": "./lib/react-screenshots.es.js", "types": "./lib/types/exports.d.ts", "files": ["lib/**", "electron/**"], "scripts": {"start": "vite", "dev": "tsc --watch", "build": "npm run lint && npm run clean && npm run build:web && npm run build:electron && npm run build:lib", "build:web": "vite build --mode web", "build:electron": "vite build --mode electron", "build:lib": "vite build --mode lib && tsc --project tsconfig.build.json", "lint": "eslint src --ext .js,.ts,.jsx,.tsx --fix", "preview": "vite preview", "clean": "<PERSON><PERSON>f dist lib electron"}, "repository": {"type": "git", "url": "git+https://github.com/nashaofu/screenshots.git"}, "keywords": ["screenshot", "cropper", "react"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nashaofu/screenshots/issues"}, "homepage": "https://github.com/nashaofu/screenshots/tree/master/packages/react-screenshots#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}, "devDependencies": {"@types/react": "^18.0.31", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.37.0", "eslint-config-standard": "^17.0.0", "eslint-config-standard-jsx": "^11.0.0", "eslint-config-standard-react": "^13.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "less": "^4.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^4.4.1", "typescript": "^5.0.2", "vite": "^4.2.1"}}