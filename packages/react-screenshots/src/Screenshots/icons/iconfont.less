@font-face {
  font-family: 'screenshots-icon'; /* Project id 572327 */
  src: url('iconfont.woff2') format('woff2'), url('iconfont.woff') format('woff'),
    url('iconfont.ttf') format('truetype');
}

[class^='icon-'],
[class*=' icon-'] {
  font-family: 'screenshots-icon' !important;
  font-style: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ok:before {
  content: '\e001';
}

.icon-cancel:before {
  content: '\e002';
}

.icon-save:before {
  content: '\e003';
}

.icon-redo:before {
  content: '\e004';
}

.icon-undo:before {
  content: '\e005';
}

.icon-mosaic:before {
  content: '\e006';
}

.icon-text:before {
  content: '\e007';
}

.icon-brush:before {
  content: '\e008';
}

.icon-arrow:before {
  content: '\e009';
}

.icon-ellipse:before {
  content: '\e00a';
}

.icon-rectangle:before {
  content: '\e00b';
}

.icon-translate:before {
  content: '译';
  // background: url(translate.svg) no-repeat center;
  // background-size: contain;
  // width: 16px;
  // height: 16px;
  // display: inline-block;
}

.icon-ocr:before {
  content: '识';
  // background: url(ocr.svg) no-repeat center;
  // background-size: contain;
  // width: 16px;
  // height: 16px;
  // display: inline-block;
}
