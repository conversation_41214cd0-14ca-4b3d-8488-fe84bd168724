@import '../var.less';

.screenshots-magnifier {
  position: absolute;
  font-family: @font-family;
  left: 0;
  top: 0;
  width: 100px;
  box-shadow: 0 0 8px 0px #000;
  z-index: 9;

  &,
  * {
    box-sizing: border-box;
    user-select: none;
  }

  &-body {
    position: relative;
    background-color: #fff;
    &:before {
      content: '';
      background-color: rgb(10, 114, 161);
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 2px;
      z-index: 1;
    }
    &:after {
      content: '';
      background-color: rgb(10, 114, 161);
      position: absolute;
      top: 0;
      left: 50%;
      width: 2px;
      height: 100%;
      z-index: 1;
    }
    &-canvas {
      display: block;
      width: 100px;
      height: 80px;
    }
  }
  &-footer {
    height: 40px;
    color: #fff;
    font-size: 11px;
    background-color: rgb(95, 94, 94);
    padding: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
    &-item {
      height: 18px;
      line-height: 18px;
    }
  }
}
