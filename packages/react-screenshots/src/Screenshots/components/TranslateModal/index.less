@import '../../var.less';

.translate-modal {
  .screenshots-modal-content {
    padding: 0;
  }
}

.translate-modal-container {
  display: flex;
  height: 400px;
}

.translate-modal-image {
  width: 40%;
  padding: 16px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.translate-modal-image-placeholder {
  color: #999;
  font-size: 14px;
}

.translate-modal-content {
  width: 60%;
  display: flex;
  flex-direction: column;
}

.translate-modal-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  
  &:first-child {
    border-bottom: 1px solid #f0f0f0;
  }
}

.translate-modal-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.translate-modal-copy-btn {
  background: none;
  border: none;
  color: @border-color;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 2px;
  
  &:hover {
    background-color: rgba(51, 153, 255, 0.1);
  }
}

.translate-modal-text-area {
  flex: 1;
  overflow: auto;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
}

.translate-modal-text {
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 14px;
  line-height: 1.5;
  user-select: text;
}

.translate-modal-loading {
  color: #999;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.translate-modal-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.translate-modal-close-btn {
  padding: 6px 16px;
  background-color: @border-color;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: darken(@border-color, 10%);
  }
}