import { Display } from './electron/app'
import { Bounds } from './Screenshots/types'

type ScreenshotsListener = (...args: never[]) => void

interface ScreenshotsData {
  bounds: Bounds
  display: Display
}

interface GlobalScreenshots {
  ready: () => void
  reset: () => void
  save: (arrayBuffer: <PERSON><PERSON><PERSON><PERSON>uffer, data: ScreenshotsData) => void
  cancel: () => void
  ok: (arrayBuffer: A<PERSON>yBuffer, data: ScreenshotsData) => void
  on: (channel: string, fn: ScreenshotsListener) => void
  off: (channel: string, fn: ScreenshotsListener) => void
}

declare global {
  interface Window {
    screenshots: GlobalScreenshots
  }
}
