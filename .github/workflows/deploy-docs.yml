# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Deploy Docs

on:
  push:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v2

      - name: Setup Node.js environment 🔥
        uses: actions/setup-node@v2
        with:
          node-version: 16.x
          cache: 'yarn'

      - name: Install 💫
        run: yarn --frozen-lockfile

      - name: Build 🔧
        run: yarn build

      - name: Deploy 🚀
        uses: JamesIves/github-pages-deploy-action@v4.2.2
        with:
          branch: gh-pages # The branch the action should deploy to.
          folder: packages/react-screenshots/dist # The folder the action should deploy.
